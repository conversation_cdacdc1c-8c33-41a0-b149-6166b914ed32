<?php

use think\migration\Migrator;
use Phinx\Db\Adapter\MysqlAdapter;

class IpW<PERSON>elist extends Migrator
{
    /**
     * 创建IP白名单表
     */
    public function change(): void
    {
        $this->ipWhitelist();
    }

    /**
     * 创建IP白名单表
     */
    public function ipWhitelist(): void
    {
        if (!$this->hasTable('ip_whitelist')) {
            $table = $this->table('ip_whitelist', [
                'id'          => false,
                'comment'     => 'IP白名单表',
                'row_format'  => 'DYNAMIC',
                'primary_key' => 'id',
                'collation'   => 'utf8mb4_unicode_ci',
            ]);
            $table->addColumn('id', 'integer', ['comment' => 'ID', 'signed' => false, 'identity' => true, 'null' => false])
                ->addColumn('ip', 'string', ['limit' => 50, 'default' => '', 'comment' => 'IP地址或CIDR网段', 'null' => false])
                ->addColumn('name', 'string', ['limit' => 100, 'default' => '', 'comment' => '名称/备注', 'null' => false])
                ->addColumn('type', 'enum', ['values' => 'ipv4,ipv6,cidr', 'default' => 'ipv4', 'comment' => 'IP类型:ipv4=IPv4地址,ipv6=IPv6地址,cidr=CIDR网段', 'null' => false])
                ->addColumn('status', 'enum', ['values' => '0,1', 'default' => '1', 'comment' => '状态:0=禁用,1=启用', 'null' => false])
                ->addColumn('admin_id', 'integer', ['comment' => '创建管理员ID', 'default' => 0, 'signed' => false, 'null' => false])
                ->addColumn('remark', 'string', ['limit' => 255, 'default' => '', 'comment' => '备注说明', 'null' => false])
                ->addColumn('createtime', 'biginteger', ['signed' => false, 'null' => true, 'default' => null, 'comment' => '创建时间'])
                ->addColumn('updatetime', 'biginteger', ['signed' => false, 'null' => true, 'default' => null, 'comment' => '更新时间'])
                ->addIndex(['ip'], ['unique' => true])
                ->addIndex(['status'])
                ->create();
        }
    }
} 