<?php

namespace app\common\model;

use think\Model;
use think\facade\Cache;

/**
 * IP白名单模型
 */
class IpWhitelist extends Model
{
    /**
     * 数据表主键
     * @var string
     */
    protected $pk = 'id';

    /**
     * 数据表名称
     * @var string
     */
    protected $table = 'ip_whitelist';

    /**
     * 自动写入时间戳
     * @var bool
     */
    protected $autoWriteTimestamp = true;

    /**
     * 创建时间字段
     * @var string
     */
    protected $createTime = 'createtime';

    /**
     * 更新时间字段
     * @var string
     */
    protected $updateTime = 'updatetime';

    /**
     * 缓存键名
     */
    const CACHE_KEY = 'ip_whitelist_cache';

    /**
     * 缓存有效期（秒）
     */
    const CACHE_EXPIRE = 3600;

    /**
     * 获取所有启用的IP白名单
     * @param bool $useCache 是否使用缓存
     * @return array
     */
    public static function getActiveWhitelist(bool $useCache = true): array
    {
        if ($useCache) {
            $whitelist = Cache::get(self::CACHE_KEY);
            if ($whitelist !== false) {
                return $whitelist;
            }
        }

        $list = self::where('status', '1')
            ->order('id', 'asc')
            ->select()
            ->toArray();

        $whitelist = [];
        foreach ($list as $item) {
            $whitelist[] = $item['ip'];
        }

        if ($useCache) {
            Cache::set(self::CACHE_KEY, $whitelist, self::CACHE_EXPIRE);
        }

        return $whitelist;
    }

    /**
     * 添加IP到白名单
     * @param string $ip IP地址或CIDR网段
     * @param string $name 名称
     * @param string $remark 备注
     * @param int $adminId 管理员ID
     * @return bool
     */
    public static function addIp(string $ip, string $name = '', string $remark = '', int $adminId = 0): bool
    {
        // 检查IP是否已存在
        if (self::where('ip', $ip)->find()) {
            return false;
        }

        // 判断IP类型
        $type = self::getIpType($ip);
        if (!$type) {
            return false;
        }

        $data = [
            'ip' => $ip,
            'name' => $name ?: $ip,
            'type' => $type,
            'status' => '1',
            'admin_id' => $adminId,
            'remark' => $remark,
        ];

        $result = self::create($data);
        if ($result) {
            self::clearCache();
        }

        return (bool)$result;
    }

    /**
     * 移除IP白名单
     * @param string $ip IP地址
     * @return bool
     */
    public static function removeIp(string $ip): bool
    {
        $result = self::where('ip', $ip)->delete();
        if ($result) {
            self::clearCache();
        }
        return (bool)$result;
    }

    /**
     * 启用/禁用IP白名单
     * @param string $ip IP地址
     * @param string $status 状态 0=禁用 1=启用
     * @return bool
     */
    public static function toggleStatus(string $ip, string $status): bool
    {
        $result = self::where('ip', $ip)->update(['status' => $status]);
        if ($result) {
            self::clearCache();
        }
        return (bool)$result;
    }

    /**
     * 判断IP类型
     * @param string $ip IP地址或CIDR网段
     * @return string|false 返回类型或false
     */
    protected static function getIpType(string $ip)
    {
        // 检查是否为CIDR格式
        if (strpos($ip, '/') !== false) {
            [$ipAddr, $mask] = explode('/', $ip);
            if (filter_var($ipAddr, FILTER_VALIDATE_IP, FILTER_FLAG_IPV4) && is_numeric($mask) && $mask >= 0 && $mask <= 32) {
                return 'cidr';
            }
            if (filter_var($ipAddr, FILTER_VALIDATE_IP, FILTER_FLAG_IPV6) && is_numeric($mask) && $mask >= 0 && $mask <= 128) {
                return 'cidr';
            }
            return false;
        }

        // 检查IPv4
        if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_IPV4)) {
            return 'ipv4';
        }

        // 检查IPv6
        if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_IPV6)) {
            return 'ipv6';
        }

        return false;
    }

    /**
     * 清除缓存
     */
    public static function clearCache(): void
    {
        Cache::delete(self::CACHE_KEY);
    }

    /**
     * 初始化默认白名单
     * @return bool
     */
    public static function initDefaultWhitelist(): bool
    {
        $defaultList = [
            ['ip' => '127.0.0.1', 'name' => '本地回环地址', 'type' => 'ipv4', 'remark' => '系统默认'],
            ['ip' => '::1', 'name' => 'IPv6本地回环地址', 'type' => 'ipv6', 'remark' => '系统默认'],
            ['ip' => '***********/16', 'name' => '私有网络段A', 'type' => 'cidr', 'remark' => '系统默认'],
            ['ip' => '10.0.0.0/8', 'name' => '私有网络段B', 'type' => 'cidr', 'remark' => '系统默认'],
            ['ip' => '**********/12', 'name' => '私有网络段C', 'type' => 'cidr', 'remark' => '系统默认'],
        ];

        foreach ($defaultList as $item) {
            if (!self::where('ip', $item['ip'])->find()) {
                self::create($item);
            }
        }

        self::clearCache();
        return true;
    }

    /**
     * 模型事件 - 数据更新后清除缓存
     */
    public static function onAfterInsert(): void
    {
        self::clearCache();
    }

    /**
     * 模型事件 - 数据更新后清除缓存
     */
    public static function onAfterUpdate(): void
    {
        self::clearCache();
    }

    /**
     * 模型事件 - 数据删除后清除缓存
     */
    public static function onAfterDelete(): void
    {
        self::clearCache();
    }
} 