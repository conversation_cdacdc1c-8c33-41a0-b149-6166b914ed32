<?php

use think\migration\Migrator;
use think\facade\Db;

class IpWhitelistData extends Migrator
{
    /**
     * 初始化IP白名单数据
     */
    public function up(): void
    {
        // 检查表是否存在
        if (!$this->hasTable('ip_whitelist')) {
            return;
        }

        // 初始化默认IP白名单数据
        $defaultData = [
            [
                'ip' => '127.0.0.1',
                'name' => '本地回环地址',
                'type' => 'ipv4',
                'status' => '1',
                'admin_id' => 0,
                'remark' => '系统默认 - 本地开发必需',
                'createtime' => time(),
                'updatetime' => time(),
            ],
            [
                'ip' => '::1',
                'name' => 'IPv6本地回环地址',
                'type' => 'ipv6',
                'status' => '1',
                'admin_id' => 0,
                'remark' => '系统默认 - IPv6本地开发必需',
                'createtime' => time(),
                'updatetime' => time(),
            ],
            [
                'ip' => '***********/16',
                'name' => '私有网络段A',
                'type' => 'cidr',
                'status' => '1',
                'admin_id' => 0,
                'remark' => '系统默认 - 私有网络段',
                'createtime' => time(),
                'updatetime' => time(),
            ],
            [
                'ip' => '10.0.0.0/8',
                'name' => '私有网络段B',
                'type' => 'cidr',
                'status' => '1',
                'admin_id' => 0,
                'remark' => '系统默认 - 私有网络段',
                'createtime' => time(),
                'updatetime' => time(),
            ],
            [
                'ip' => '**********/12',
                'name' => '私有网络段C',
                'type' => 'cidr',
                'status' => '1',
                'admin_id' => 0,
                'remark' => '系统默认 - 私有网络段',
                'createtime' => time(),
                'updatetime' => time(),
            ],
        ];

        // 插入默认数据（如果不存在）
        foreach ($defaultData as $item) {
            $exists = Db::name('ip_whitelist')->where('ip', $item['ip'])->find();
            if (!$exists) {
                Db::name('ip_whitelist')->insert($item);
            }
        }
    }

    /**
     * 回滚操作
     */
    public function down(): void
    {
        // 删除默认数据
        $defaultIps = [
            '127.0.0.1',
            '::1',
            '***********/16',
            '10.0.0.0/8',
            '**********/12',
        ];

        foreach ($defaultIps as $ip) {
            Db::name('ip_whitelist')->where('ip', $ip)->where('remark', 'like', '系统默认%')->delete();
        }
    }
} 