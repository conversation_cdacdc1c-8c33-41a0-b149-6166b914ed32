<?php

namespace app\common\middleware;

use Closure;
use think\Request;
use think\Response;
use think\facade\Config;
use think\exception\HttpException;
use app\common\model\IpWhitelist as IpWhitelistModel;

/**
 * IP白名单中间件
 * 只允许白名单中的IP地址访问网站
 */
class IpWhitelist
{
    /**
     * 默认IP白名单配置（作为备用）
     * 当数据库中没有数据时使用
     */
    protected array $defaultWhitelist = [
        '127.0.0.1',     // 本地回环地址
        '::1',           // IPv6本地回环地址
        '***********/16', // 私有网络段
        '10.0.0.0/8',    // 私有网络段
        '**********/12', // 私有网络段
    ];

    /**
     * 处理请求
     * @param Request $request
     * @param Closure $next
     * @return Response
     */
    public function handle(Request $request, Closure $next): Response
    {
        // 检查是否启用IP白名单功能
        if (!Config::get('buildadmin.enable_ip_whitelist', true)) {
            return $next($request);
        }

        // 获取客户端真实IP
        $clientIp = $this->getRealIp($request);
        
        // 获取IP白名单
        $whitelist = $this->getWhitelist();
        
        // 检查IP是否在白名单中
        if (!$this->isIpInWhitelist($clientIp, $whitelist)) {
            // IP不在白名单中，拒绝访问
            throw new HttpException(403, "访问被拒绝：您的IP地址 [{$clientIp}] 不在允许的访问列表中");
        }
        
        // IP在白名单中，允许继续访问
        return $next($request);
    }

    /**
     * 获取客户端真实IP地址
     * 支持代理、CDN、负载均衡等环境
     * @param Request $request
     * @return string
     */
    protected function getRealIp(Request $request): string
    {
        // 优先获取代理服务器转发的真实IP
        $headers = [
            'HTTP_CF_CONNECTING_IP',     // Cloudflare
            'HTTP_X_FORWARDED_FOR',      // 通用代理
            'HTTP_X_FORWARDED',          // 代理
            'HTTP_X_CLUSTER_CLIENT_IP',  // 集群
            'HTTP_FORWARDED_FOR',        // 代理
            'HTTP_FORWARDED',            // 代理
            'HTTP_CLIENT_IP',            // 客户端IP
            'HTTP_X_REAL_IP',            // Nginx代理
        ];

        foreach ($headers as $header) {
            if (isset($_SERVER[$header]) && !empty($_SERVER[$header])) {
                $ips = explode(',', $_SERVER[$header]);
                $ip = trim($ips[0]);
                if ($this->isValidIp($ip)) {
                    return $ip;
                }
            }
        }

        // 获取远程IP
        return $request->ip();
    }

    /**
     * 获取IP白名单
     * @return array
     */
    protected function getWhitelist(): array
    {
        try {
            // 从数据库获取IP白名单
            $dbWhitelist = IpWhitelistModel::getActiveWhitelist();
            
            // 如果数据库有数据，则使用数据库数据
            if (!empty($dbWhitelist)) {
                return $dbWhitelist;
            }
            
            // 如果数据库没有数据，初始化默认白名单到数据库
            IpWhitelistModel::initDefaultWhitelist();
            
            // 再次获取数据库白名单
            $dbWhitelist = IpWhitelistModel::getActiveWhitelist(false);
            if (!empty($dbWhitelist)) {
                return $dbWhitelist;
            }
            
        } catch (\Exception $e) {
            // 如果数据库操作失败，记录错误日志并使用默认白名单
            \think\facade\Log::error('IP白名单数据库操作失败: ' . $e->getMessage());
        }
        
        // 从配置文件获取IP白名单（兼容旧版本）
        $configWhitelist = Config::get('buildadmin.ip_whitelist', []);
        
        // 合并配置的白名单和默认白名单
        return array_merge($this->defaultWhitelist, $configWhitelist);
    }

    /**
     * 检查IP是否在白名单中
     * @param string $ip
     * @param array $whitelist
     * @return bool
     */
    protected function isIpInWhitelist(string $ip, array $whitelist): bool
    {
        foreach ($whitelist as $allowedIp) {
            // 检查是否为CIDR格式的网络段
            if (strpos($allowedIp, '/') !== false) {
                if ($this->isIpInCidr($ip, $allowedIp)) {
                    return true;
                }
            } else {
                // 直接IP比较
                if ($ip === $allowedIp) {
                    return true;
                }
            }
        }
        
        return false;
    }

    /**
     * 检查IP是否在CIDR网络段中
     * @param string $ip
     * @param string $cidr
     * @return bool
     */
    protected function isIpInCidr(string $ip, string $cidr): bool
    {
        if (!$this->isValidIp($ip)) {
            return false;
        }

        [$network, $mask] = explode('/', $cidr);
        
        if (!$this->isValidIp($network)) {
            return false;
        }

        // IPv4 CIDR检查
        if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_IPV4) && 
            filter_var($network, FILTER_VALIDATE_IP, FILTER_FLAG_IPV4)) {
            
            $ipLong = ip2long($ip);
            $networkLong = ip2long($network);
            $maskLong = -1 << (32 - (int)$mask);
            
            return ($ipLong & $maskLong) === ($networkLong & $maskLong);
        }

        // IPv6 CIDR检查
        if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_IPV6) && 
            filter_var($network, FILTER_VALIDATE_IP, FILTER_FLAG_IPV6)) {
            
            $ipBin = inet_pton($ip);
            $networkBin = inet_pton($network);
            $maskBits = (int)$mask;
            
            if ($ipBin === false || $networkBin === false) {
                return false;
            }
            
            // 计算需要比较的字节数
            $byteCount = intval($maskBits / 8);
            $bitCount = $maskBits % 8;
            
            // 比较完整字节
            if ($byteCount > 0 && substr($ipBin, 0, $byteCount) !== substr($networkBin, 0, $byteCount)) {
                return false;
            }
            
            // 比较剩余位
            if ($bitCount > 0 && $byteCount < 16) {
                $mask = 0xFF << (8 - $bitCount);
                $ipByte = ord($ipBin[$byteCount]);
                $networkByte = ord($networkBin[$byteCount]);
                
                return ($ipByte & $mask) === ($networkByte & $mask);
            }
            
            return true;
        }

        return false;
    }

    /**
     * 验证IP地址格式是否正确
     * @param string $ip
     * @return bool
     */
    protected function isValidIp(string $ip): bool
    {
        return filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE) !== false ||
               filter_var($ip, FILTER_VALIDATE_IP) !== false;
    }
} 